.report-main-container {
  background-color: var(--color-white);
}
.report-table-container {
  margin-top: var(--spacing-lg);
}

.report-header-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  border-bottom: var(--normal-sec-border);

  .report-header-tabs-wrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;

    .MuiTabs-root {
      border-bottom: none;
    }
  }
  @media (max-width: 899px) {
    overflow-x: auto;
    ::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);

  .report-title {
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .report-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.staff-view-container,
.staff-edit-container {
  .staff-view-header,
  .staff-edit-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--color-border);
  }

  .staff-details-content {
    .staff-additional-info {
      margin-top: var(--spacing-lg);

      .section-title {
        margin-bottom: var(--spacing-md);
        font-weight: 600;
        color: var(--color-text-primary);
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);

        .info-item {
          padding: var(--spacing-md);
          border: 1px solid var(--color-border);
          border-radius: var(--border-radius-sm);
          background-color: var(--color-background-light);
        }
      }
    }
  }

  .staff-edit-content {
    .staff-edit-actions {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: flex-start;
    }
  }
}

.status-chip {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;

  &.active {
    background-color: var(--color-success-light);
    color: var(--color-success);
  }

  &.pending {
    background-color: var(--color-warning-light);
    color: var(--color-warning);
  }

  &.inactive {
    background-color: var(--color-error-light);
    color: var(--color-error);
  }

  &.ongoing {
    background-color: var(--color-info-light);
    color: var(--color-info);
  }

  &.awaiting {
    background-color: var(--color-secondary-light);
    color: var(--color-secondary);
  }
}

// The CreateStaff and UserProfile components will display normally in reports context
// The back button functionality is overridden via router.push override in the component
.report-main-container {
  .reports-edit-staff-wrapper {
    .section-right {
      box-shadow: none;
      .section-right-tab-header {
        padding: 0;
      }
      .section-right-content {
        padding: 0;
      }
    }
  }
}
