body {
  .common-table-container {
    width: 100%;
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    border-radius: var(--border-radius-md);
    box-shadow: var(--box-shadow-xs);
    border: var(--normal-sec-border);
    .MuiTableContainer-root {
      border-radius: var(--border-radius-sm);
    }
    .MuiTableRow-root.common-table-row:hover {
      // background-color: var(--color-primary-opacity);
      background-color: var(--color-ice-blue);
    }
    .MuiTableRow-root {
      &:nth-of-type(even) {
        // background-color: var(--color-light-grayish-blue);
        // background-color: var(--color-ice-blue);
        background-color: var(--color-primary-opacity);
      }
    }
    .MuiTableCell-root {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      // word-break: break-word;
    }
    .MuiTableCell-head {
      font-weight: bold;
      background: var(--color-primary);
      color: var(--text-color-white);
    }
    .MuiTableSortLabel-root.Mui-active {
      color: var(--color-primary);
    }
    .common-table-pagination {
      margin: var(--spacing-none) var(--spacing-md);
      padding-bottom: var(--spacing-md);
    }
  }
  .table-action-menu {
    .MuiPaper-root {
      width: 180px;
      background-color: var(--color-white);
      border: 1px solid var(--border-color-light-gray);
      border-radius: var(--border-radius-md);
      box-shadow: var(--box-shadow-xs);
      .MuiList-root {
        padding: var(--spacing-none);
        .MuiMenuItem-root {
          font-family: var(--font-family-primary);
          gap: var(--spacing-sm);
          padding: var(--spacing-xsm) var(--spacing-md);
          font-size: var(--font-size-sm);
          color: var(--text-color-slate-gray);
          min-height: auto;
          &:hover {
            background-color: var(--color-primary-opacity);
            color: var(--color-primary);
          }

          &.danger {
            color: var(--color-danger);

            &:hover {
              background-color: var(--color-danger-opacity);
              color: var(--color-danger);
            }
          }
        }
      }
    }
  }
}
