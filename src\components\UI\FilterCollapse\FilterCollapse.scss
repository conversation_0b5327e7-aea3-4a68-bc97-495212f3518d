.filter-collapse-container {
  .MuiPaper-root {
    background: var(--color-white);
    border-radius: var(--border-radius-md) !important;
    box-shadow: var(--box-shadow-xs);
    width: 100%;
    max-width: 100%;
    border: var(--normal-sec-border);
    .filter-collapse-header {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      min-height: 48px;
      .MuiAccordionSummary-content {
        margin: var(--spacing-md) var(--spacing-none);
        align-items: center;
      }
      .filter-collapse-icon {
        display: flex;
        align-items: center;
        margin-right: var(--spacing-xs);
        svg {
          display: block;
          fill: var(--icon-color-primary);
        }
      }
      .filter-collapse-title {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--text-color-black);
        margin-right: auto;
      }
    }
    .filter-collapse-accordion-details {
      border-top: var(--normal-sec-border);
      padding: var(--spacing-lg);
      .filter-collapse-fields {
        width: 100%;
        .filter-fields-row {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
          gap: var(--spacing-lg);
          // @media (max-width: 900px) {
          //   grid-template-columns: 1fr;
          // }
          .filter-field {
            display: flex;
            flex-direction: column;
            min-width: 220px;
          }
        }
      }
    }
  }
  .filter-collapse-action-row {
    margin-top: var(--spacing-md);
    display: flex;
    justify-content: flex-end;
  }
}
