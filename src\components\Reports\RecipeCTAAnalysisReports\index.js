'use client';
import React, { useState } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import RecipeCTAReportsTable from './RecipeCTAReportsTable';
import { staticOptions } from '@/helper/common/staticOptions';
import '../reports.scss';

export default function RecipeCTAAnalysisReports() {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    ctaType: '',
    dateRange: null,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Recipe Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'ctaType',
      type: 'select',
      label: 'CTA Type',
      options: staticOptions?.ctaTypes,
    },
    {
      name: 'dateRange',
      type: 'dateRange',
      label: 'Date Range',
    },
  ];

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  return (
    <Box className="report-main-container">
      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <RecipeCTAReportsTable searchValue={searchValue} filters={filters} />
    </Box>
  );
}
