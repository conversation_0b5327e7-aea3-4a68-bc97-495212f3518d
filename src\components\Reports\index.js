'use client';
import React, { useState, useLayoutEffect } from 'react';
import { Box, Divider, Popover, Tooltip, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import CustomTabs from '@/components/UI/CustomTabs';
import { useRouter, useSearchParams } from 'next/navigation';
import { reportsMenuList } from '@/helper/common/commonMenus';
import CustomButton from '../UI/CustomButton';
import DownloadIcon from '@mui/icons-material/Download';
import './reports.scss';

const Reports = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);

  const isReport = searchParams.get('is_report');
  const isActiveTab = searchParams.get('is_tab');
  const queryParams = new URLSearchParams(searchParams);

  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const exportOpen = Boolean(exportAnchorEl);
  const exportId = exportOpen ? 'export-popover' : undefined;

  // Initialize from URL parameters
  useLayoutEffect(() => {
    setActiveMenuItem(Number(isReport || 1));
  }, [isReport]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    setActiveTab(tabIndex);
  }, [isActiveTab]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveTab(1); // Reset to first tab when menu item changes
    router.push(`/reports?is_report=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    queryParams.set('is_tab', tabId);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );
    return currentItem?.tabs || [];
  };

  const getCurrentComponent = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );

    // If item has tabs, check for tab-specific component
    if (currentItem?.tabs && currentItem.tabs.length > 0) {
      const currentTab = currentItem.tabs.find((tab) => tab.id === activeTab);
      return currentTab?.component || currentItem?.component || null;
    }

    // If no tabs, return main component
    return currentItem?.component || null;
  };

  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  const handleExportDownload = async (format) => {
    console.error(format);
    // try {
    //   let response;
    //   if (activeTab === 1) {
    //     // Export CTA Analytics
    //     response = await exportCTAAnylytics(format);
    //   } else if (activeTab === 2) {
    //     // Export Contact Submissions
    //     response = await exportContactSubmissions(format);
    //   }
    //   // Get filename from response headers or fallback
    //   let filename = `${activeTab === 1 ? 'cta_analytics' : 'contact_submissions'}_${new Date().toISOString()?.split('T')?.[0]}.${format === 'excel' ? 'xlsx' : 'csv'}`;
    //   const disposition = response?.headers?.['content-disposition'];
    //   if (disposition) {
    //     const match = disposition.match(/filename="?([^";]+)"?/);
    //     if (match) filename = match[1];
    //   }
    //   // Create blob and trigger download from API response
    //   const blob = response?.data;
    //   const url = window.URL.createObjectURL(blob);
    //   const link = document.createElement('a');
    //   link.href = url;
    //   link.setAttribute('download', filename);
    //   document.body.appendChild(link);
    //   link.click();
    //   link?.parentNode?.removeChild(link);
    //   window.URL.revokeObjectURL(url);
    //   handleExportClose();
    // } catch {
    // Optionally show error message
    handleExportClose();
    // }
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        {/* Show tabs only if current menu item has tabs */}
        {getCurrentTabs().length > 0 && (
          <Box className="section-right-tab-header">
            <Box className="report-header-tabs">
              <Box className="report-header-tabs-wrap">
                <CustomTabs
                  tabs={getCurrentTabs()}
                  initialTab={activeTab}
                  onTabChange={handleTabChange}
                />
              </Box>
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Export Report
                      </Typography>
                    }
                    arrow
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <DownloadIcon />
                  </Tooltip>
                }
                onClick={handleExportClick}
              />
            </Box>
          </Box>
        )}
        <Box className="section-right-content">{getCurrentComponent()}</Box>
      </Box>

      <Popover
        className="export-popover"
        id={exportId}
        open={exportOpen}
        anchorEl={exportAnchorEl}
        onClose={handleExportClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Box className="export-option">
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('pdf')}
          >
            PDF
          </Typography>
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="title-text cursor-pointer fw600"
            onClick={() => handleExportDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default Reports;
